import { Controller, Get, Post, Req, Res, UnauthorizedException, Logger, HttpStatus, Body, Param, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiUnauthorizedResponse, ApiBody, ApiCookieAuth, ApiParam } from '@nestjs/swagger';
import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthSharedService } from './auth.service';
import { Public } from './auth.guard';
import { DecryptTokenDto, DecodeJwtDto } from './dto/decrypt.dto';
import * as jwt from 'jsonwebtoken';

@ApiTags('Authentication')
@Controller('auth')
export class AuthSharedController {
  private readonly logger = new Logger(AuthSharedController.name);

  constructor(private readonly authSharedService: AuthSharedService) {}







  @Get('me')
  @ApiCookieAuth('access_token')
  @ApiOperation({
    summary: 'Get current user information',
    description: 'Authenticates user using encrypted cookies, decrypts access token, verifies JWT with <PERSON><PERSON><PERSON>, and returns user information from external auth service'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User authenticated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        username: { type: 'string' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        first_name: { type: 'string' },
        last_name: { type: 'string' },
        role: { type: 'string' },
        permissions: { type: 'array', items: { type: 'string' } },
        accounts: { type: 'array' },
        partners: { type: 'array' },
        active_accounts: { type: 'array' },
        active_partners: { type: 'array' },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed - invalid or missing cookies',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Authentication failed' }
      }
    }
  })
  async getCurrentUser(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};

      this.logger.log(`🔐 [AUTH CONTROLLER] /auth/me endpoint called`);
      this.logger.log(`🍪 [AUTH CONTROLLER] Received cookies: ${JSON.stringify(Object.keys(cookies))}`);
      this.logger.log(`🔑 [AUTH CONTROLLER] Access token present: ${!!cookies.access_token}`);

      // Authenticate user using the new method that properly handles external auth service
      const user = await this.authSharedService.authenticateUserFromCookies(cookies);

      this.logger.log(`✅ [AUTH CONTROLLER] User authenticated successfully: ${user.email}`);

      return reply.status(HttpStatus.OK).send(user);
    } catch (error) {
      this.logger.error(`❌ [AUTH CONTROLLER] Authentication failed: ${error.message}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  @Get('me-test')
  @Public()
  @ApiOperation({
    summary: 'Test user information endpoint',
    description: 'Test endpoint that bypasses encryption to verify the authentication flow structure'
  })
  async getCurrentUserTest(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      this.logger.log('🧪 [AUTH CONTROLLER] /auth/me-test endpoint called');

      const cookies = (request as any).cookies || {};
      this.logger.log('🧪 [AUTH CONTROLLER] Cookies received:', Object.keys(cookies));

      // For testing, return a mock user response in the expected format
      const mockUser = {
        sub: 'test-user-123',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        accounts: [],
        partners: [],
        active_accounts: [],
        active_partners: [],
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600
      };

      this.logger.log('✅ [AUTH CONTROLLER] Test authentication successful');
      return reply.status(HttpStatus.OK).send(mockUser);
    } catch (error) {
      this.logger.error('❌ [AUTH CONTROLLER] Test authentication failed:', error.message);
      throw new UnauthorizedException('Test authentication failed');
    }
  }





  @Post('decrypt')
  @Public()
  @ApiOperation({
    summary: 'Decrypt encrypted token',
    description: 'Decrypt an encrypted access token or refresh token for testing purposes'
  })
  @ApiBody({ type: DecryptTokenDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token decrypted successfully',
    schema: {
      type: 'object',
      properties: {
        decrypted: { type: 'string', description: 'The decrypted token' },
        type: { type: 'string', description: 'Type of token (jwt or hex)' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid encrypted token format'
  })
  async decryptToken(@Body() body: DecryptTokenDto) {
    try {
      const decrypted = await this.authSharedService.decryptTokenForTesting(body.token);

      // Try to determine if it's a JWT or hex token
      let type = 'unknown';
      if (decrypted.startsWith('eyJ')) {
        type = 'jwt';
      } else if (/^[0-9a-fA-F]+$/.test(decrypted)) {
        type = 'hex';
      }

      return {
        decrypted,
        type,
        length: decrypted.length
      };
    } catch (error) {
      this.logger.error('Failed to decrypt token:', error);
      throw new UnauthorizedException('Failed to decrypt token');
    }
  }

  @Post('encrypt-test')
  @Public()
  @ApiOperation({
    summary: 'Encrypt test JWT token',
    description: 'Create an encrypted test token for testing the /auth/me endpoint'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        payload: {
          type: 'object',
          description: 'JWT payload to encrypt',
          example: {
            sub: 'test-user-123',
            email: '<EMAIL>',
            first_name: 'Test',
            last_name: 'User',
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + 3600
          }
        }
      }
    }
  })
  async encryptTestToken(@Body() body: { payload: any }) {
    try {
      const encryptedToken = await this.authSharedService.encryptTokenForTesting(JSON.stringify(body.payload));
      return {
        success: true,
        encryptedToken,
        message: 'Token encrypted successfully',
        usage: 'Use this token as access_token cookie value to test /auth/me endpoint'
      };
    } catch (error) {
      this.logger.error('Failed to encrypt token:', error);
      throw new UnauthorizedException('Failed to encrypt token');
    }
  }

  @Post('decode-jwt')
  @Public()
  @ApiOperation({
    summary: 'Decode JWT token',
    description: 'Decode a JWT token to see its payload (without verification)'
  })
  @ApiBody({ type: DecodeJwtDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'JWT decoded successfully',
    schema: {
      type: 'object',
      properties: {
        header: { type: 'object', description: 'JWT header' },
        payload: { type: 'object', description: 'JWT payload' },
        signature: { type: 'string', description: 'JWT signature' }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid JWT format'
  })
  decodeJwt(@Body() body: DecodeJwtDto) {
    try {
      const decoded = jwt.decode(body.jwt, { complete: true });

      if (!decoded) {
        throw new Error('Invalid JWT format');
      }

      return {
        header: decoded.header,
        payload: decoded.payload,
        signature: decoded.signature
      };
    } catch (error) {
      this.logger.error('Failed to decode JWT:', error);
      throw new UnauthorizedException('Failed to decode JWT');
    }
  }

  @Get('users/:id')
  @ApiCookieAuth('access_token')
  @ApiOperation({
    summary: 'Get user by ID',
    description: 'Retrieve user information by ID from external auth service using encrypted access token'
  })
  @ApiParam({
    name: 'id',
    description: 'User unique identifier (UUID)',
    type: 'string',
    format: 'uuid'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        first_name: { type: 'string' },
        last_name: { type: 'string' }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed or user not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Invalid or expired access token' }
      }
    }
  })
  async getUserById(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};

      this.logger.log(`Getting user by ID: ${id}`);

      // Get user from external auth service
      const user = await this.authSharedService.getUserById(id, cookies);

      return reply.status(HttpStatus.OK).send(user);
    } catch (error) {
      this.logger.error('Get user by ID failed:', error);
      throw new UnauthorizedException(error.message || 'Failed to retrieve user');
    }
  }

  @Get('users')
  @ApiCookieAuth('access_token')
  @ApiOperation({
    summary: 'Get all users',
    description: 'Retrieve all users from external auth service using encrypted access token. Automatically URL-decodes cookies before forwarding to external service.'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Users retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          email: { type: 'string' },
          first_name: { type: 'string' },
          last_name: { type: 'string' }
        }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Invalid or expired access token' }
      }
    }
  })
  async getAllUsers(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};

      this.logger.log('Getting all users');

      // Get users from external auth service
      const users = await this.authSharedService.getAllUsers(cookies);

      return reply.status(HttpStatus.OK).send(users);
    } catch (error) {
      this.logger.error('Get all users failed:', error);
      throw new UnauthorizedException(error.message || 'Failed to retrieve users');
    }
  }

}
